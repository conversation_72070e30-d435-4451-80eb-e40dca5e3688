# 正方形分割功能说明

## 功能概述

本功能实现了从复杂多边形中自动分割出个体正方形的计算机视觉算法。该功能能够处理以下情况：

- ✅ 多个不同边长的正方形组成的复杂多边形
- ✅ 相互重叠遮挡的正方形
- ✅ 随机旋转的正方形
- ✅ 自动补线补点绘制完整正方形轮廓

## 核心算法

### 1. 角点检测
- 使用 `cv2.goodFeaturesToTrack()` 在多边形区域内检测角点
- 采用 Harris 角点检测器，提高检测精度
- 支持质量阈值和最小距离参数调节

### 2. 正方形重构
- 从检测到的角点中生成四点组合
- 对每个四点组合进行几何验证
- 验证条件：
  - 四边长度相等（允许误差容差）
  - 四个角度接近90度
  - 综合置信度评分

### 3. 重叠处理
- 计算正方形之间的重叠面积比例
- 保留置信度更高的正方形
- 过滤重复检测结果

### 4. 补线补点绘制
- 根据检测到的顶点绘制完整正方形轮廓
- 即使部分边缘被遮挡也能完整显示
- 根据置信度调整线条粗细和颜色

## 配置参数

在 `beifen.py` 中添加了以下配置参数：

```python
# ==================== 正方形分割参数 ====================
enable_square_decomposition = True  # 是否启用正方形分割功能
corner_detection_quality = 0.01     # 角点检测质量阈值
corner_min_distance = 10            # 角点最小距离
line_detection_threshold = 50       # 直线检测阈值
square_edge_tolerance = 0.15        # 正方形边长误差容差
square_angle_tolerance = 15         # 正方形角度误差容差（度）
square_overlap_threshold = 0.3      # 正方形重叠面积阈值
min_square_area = 100              # 最小正方形面积
square_confidence_threshold = 0.5   # 正方形置信度阈值
```

## 新增函数

### 核心函数
- `decompose_polygon_to_squares()` - 主分割函数
- `validate_square_geometry()` - 正方形几何验证
- `detect_corners_in_polygon()` - 多边形内角点检测
- `generate_square_candidates_from_corners()` - 生成正方形候选
- `filter_overlapping_squares()` - 过滤重叠正方形
- `draw_complete_squares()` - 绘制完整正方形

### 辅助函数
- `calculate_angle_between_vectors()` - 计算向量夹角
- `calculate_polygon_center()` - 计算多边形中心
- `sort_vertices_clockwise()` - 顶点顺时针排序
- `calculate_squares_overlap()` - 计算正方形重叠度

## 集成方式

正方形分割功能已集成到现有的多边形检测流程中：

1. **触发条件**：检测到多边形且面积大于最小正方形面积的2倍
2. **处理位置**：在多边形检测完成后立即进行正方形分割
3. **结果显示**：分割出的正方形用青色绘制，显示置信度
4. **统计更新**：自动更新正方形统计信息

## 使用效果

### 视觉效果
- 🟣 紫色：原始多边形轮廓
- 🔵 青色：分割出的正方形（补线补点）
- 📊 显示每个正方形的置信度分数

### 统计信息
- 屏幕显示：`Squares: X, Avg Edge: XXXpx (XX.XXcm)`
- 控制台输出：`正方形: X 个, 平均边长: XX.XX 像素 (XX.XX 厘米)`

## 性能优化

1. **ROI限制**：只在多边形区域内进行处理
2. **候选限制**：限制角点组合数量，避免计算爆炸
3. **置信度过滤**：只保留高置信度的正方形
4. **重叠过滤**：避免重复检测同一正方形

## 调试功能

- 详细的控制台输出，显示检测过程
- 置信度分数显示
- 错误处理和异常捕获
- 可配置的调试参数

## 测试验证

提供了 `test_square_decomposition.py` 测试脚本：
- 角点检测功能测试
- 正方形几何验证测试
- 结果可视化生成

## 使用建议

1. **参数调节**：根据实际应用场景调整检测参数
2. **面积阈值**：设置合适的最小正方形面积
3. **置信度阈值**：调整置信度阈值以平衡检测精度和召回率
4. **重叠阈值**：根据正方形重叠程度调整重叠阈值

## 注意事项

- 功能需要在预处理完成后才能正常工作
- 对图像质量和光照条件有一定要求
- 极度变形或模糊的正方形可能无法正确识别
- 建议在稳定的环境下使用以获得最佳效果
