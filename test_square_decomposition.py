#!/usr/bin/env python3
"""
正方形分割功能测试脚本
测试从复杂多边形中分割出个体正方形的功能
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt

def create_test_polygon_with_squares():
    """创建包含多个重叠正方形的测试多边形"""
    # 创建一个空白图像
    img = np.zeros((400, 400), dtype=np.uint8)
    
    # 定义几个正方形的顶点（不同大小和旋转角度）
    squares = [
        # 正方形1：50x50，无旋转
        [(100, 100), (150, 100), (150, 150), (100, 150)],
        
        # 正方形2：40x40，旋转45度，部分重叠
        [(130, 80), (158, 108), (130, 136), (102, 108)],
        
        # 正方形3：60x60，旋转30度
        [(200, 150), (252, 180), (222, 232), (170, 202)],
        
        # 正方形4：35x35，无旋转
        [(250, 250), (285, 250), (285, 285), (250, 285)]
    ]
    
    # 绘制每个正方形
    for square in squares:
        pts = np.array(square, dtype=np.int32)
        cv2.fillPoly(img, [pts], 255)
    
    # 添加一些噪声边缘
    cv2.rectangle(img, (50, 300), (80, 330), 128, -1)
    cv2.circle(img, (320, 100), 25, 180, -1)
    
    return img, squares

def test_corner_detection():
    """测试角点检测功能"""
    print("🔍 测试角点检测功能...")
    
    # 创建测试图像
    test_img, expected_squares = create_test_polygon_with_squares()
    
    # 找到轮廓
    contours, _ = cv2.findContours(test_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        print("❌ 未找到轮廓")
        return False
    
    # 选择最大的轮廓作为多边形
    largest_contour = max(contours, key=cv2.contourArea)
    
    # 多边形近似
    epsilon = 0.02 * cv2.arcLength(largest_contour, True)
    polygon_approx = cv2.approxPolyDP(largest_contour, epsilon, True)
    
    print(f"✅ 多边形顶点数: {len(polygon_approx)}")
    
    # 测试角点检测
    corners = cv2.goodFeaturesToTrack(
        test_img,
        maxCorners=100,
        qualityLevel=0.01,
        minDistance=10,
        blockSize=3,
        useHarrisDetector=True,
        k=0.04
    )
    
    if corners is not None:
        corners = np.int32(corners).reshape(-1, 2)
        print(f"✅ 检测到 {len(corners)} 个角点")
        return True
    else:
        print("❌ 未检测到角点")
        return False

def test_square_validation():
    """测试正方形几何验证功能"""
    print("🔍 测试正方形几何验证功能...")
    
    # 测试用例1：完美正方形
    perfect_square = [(0, 0), (100, 0), (100, 100), (0, 100)]
    
    # 计算边长
    edges = []
    for i in range(4):
        p1 = perfect_square[i]
        p2 = perfect_square[(i+1) % 4]
        edge_length = np.sqrt((p1[0]-p2[0])**2 + (p1[1]-p2[1])**2)
        edges.append(edge_length)
    
    # 检查边长是否相等
    avg_edge = np.mean(edges)
    edge_tolerance = 0.15
    
    edge_valid = True
    for edge in edges:
        if abs(edge - avg_edge) / avg_edge > edge_tolerance:
            edge_valid = False
            break
    
    print(f"✅ 完美正方形边长验证: {'通过' if edge_valid else '失败'}")
    
    # 测试用例2：矩形（应该失败）
    rectangle = [(0, 0), (150, 0), (150, 100), (0, 100)]
    
    edges = []
    for i in range(4):
        p1 = rectangle[i]
        p2 = rectangle[(i+1) % 4]
        edge_length = np.sqrt((p1[0]-p2[0])**2 + (p1[1]-p2[1])**2)
        edges.append(edge_length)
    
    avg_edge = np.mean(edges)
    edge_valid = True
    for edge in edges:
        if abs(edge - avg_edge) / avg_edge > edge_tolerance:
            edge_valid = False
            break
    
    print(f"✅ 矩形边长验证: {'失败（正确）' if not edge_valid else '通过（错误）'}")
    
    return True

def visualize_test_results():
    """可视化测试结果"""
    print("🎨 生成测试结果可视化...")
    
    # 创建测试图像
    test_img, expected_squares = create_test_polygon_with_squares()
    
    # 创建彩色图像用于显示结果
    result_img = cv2.cvtColor(test_img, cv2.COLOR_GRAY2BGR)
    
    # 绘制预期的正方形
    colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0)]
    for i, square in enumerate(expected_squares):
        pts = np.array(square, dtype=np.int32)
        color = colors[i % len(colors)]
        cv2.polylines(result_img, [pts], True, color, 2)
        
        # 标记顶点
        for pt in square:
            cv2.circle(result_img, pt, 3, color, -1)
    
    # 保存结果图像
    cv2.imwrite('test_square_decomposition_result.png', result_img)
    print("✅ 测试结果已保存到 test_square_decomposition_result.png")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始正方形分割功能测试")
    print("=" * 50)
    
    # 运行各项测试
    tests = [
        ("角点检测", test_corner_detection),
        ("正方形验证", test_square_validation),
        ("结果可视化", visualize_test_results)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！正方形分割功能基础组件工作正常。")
    else:
        print("⚠️  部分测试失败，需要检查实现。")

if __name__ == "__main__":
    main()
